@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: ui-monospace, monospace;

  /* Custom animations */
  --animate-in: fadeIn 0.3s ease-out;
  --animate-slide-in-from-top-1: slideInFromTop1 0.2s ease-out;
  --animate-slide-in-from-top-2: slideInFromTop2 0.3s ease-out;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Inter, system-ui, sans-serif;
}

/* Custom components */
.container {
  @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
}

.section-padding {
  @apply py-16 sm:py-20 lg:py-24;
}

.gradient-text {
  @apply bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent;
}

.btn-primary {
  @apply inline-flex items-center justify-center rounded-md bg-blue-600 px-6 py-3 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;
}

.btn-secondary {
  @apply inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-6 py-3 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Horizontal scrollbar for tables */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(209 213 219) rgb(243 244 246);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

.scrollbar-thumb-gray-300 {
  scrollbar-color: rgb(209 213 219) transparent;
}

.scrollbar-track-gray-100 {
  scrollbar-color: transparent rgb(243 244 246);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Grid pattern background */
.bg-grid-slate-100 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(241 245 249)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

.bg-grid-slate-700\/25 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(51 65 85 / 0.25)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

/* Additional animations for sign-in page */
@keyframes slideInFromTop1 {
  from { transform: translateY(-4px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInFromTop2 {
  from { transform: translateY(-8px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-in {
  animation: var(--animate-in);
}

.slide-in-from-top-1 {
  animation: var(--animate-slide-in-from-top-1);
}

.slide-in-from-top-2 {
  animation: var(--animate-slide-in-from-top-2);
}

/* Backdrop blur support */
.backdrop-blur-xl {
  backdrop-filter: blur(24px);
}

/* Custom focus styles */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--color-blue-500);
}

/* Autofill styles */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: #1e293b !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* Dark mode autofill */
.dark input:-webkit-autofill,
.dark input:-webkit-autofill:hover,
.dark input:-webkit-autofill:focus,
.dark input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px #334155 inset !important;
  -webkit-text-fill-color: #f8fafc !important;
}

/* Autofill detection helper */
input:-webkit-autofill + label {
  transform: translateY(-1.5rem) scale(0.75);
  color: #3b82f6;
}

/* Performance-optimized animations for homepage */
@keyframes heroContentAnimate {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes statsAnimate {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes sectionHeader {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes serviceCard {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes clientLogosScroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes floatingOrb1 {
  0%, 100% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

@keyframes floatingOrb2 {
  0%, 100% {
    transform: translate(0px, 0px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: translate(40px, -25px) rotateX(180deg) rotateY(90deg);
  }
}

@keyframes floatingOrb3 {
  0%, 100% {
    transform: translate(0px, 0px) rotate(0deg);
  }
  25% {
    transform: translate(15px, -25px) rotate(120deg);
  }
  50% {
    transform: translate(-10px, 10px) rotate(240deg);
  }
  75% {
    transform: translate(20px, 5px) rotate(360deg);
  }
}

/* CSS classes for animations */
.hero-content-animate {
  animation: heroContentAnimate 0.8s ease-out forwards;
  opacity: 0;
}

.hero-content-animate:nth-child(1) { animation-delay: 0s; }
.hero-content-animate:nth-child(2) { animation-delay: 0.2s; }
.hero-content-animate:nth-child(3) { animation-delay: 0.4s; }
.hero-content-animate:nth-child(4) { animation-delay: 0.6s; }
.hero-content-animate:nth-child(5) { animation-delay: 0.8s; }

.stats-animate {
  animation: statsAnimate 0.6s ease-out forwards;
  opacity: 0;
}

.section-header {
  animation: sectionHeader 0.6s ease-out forwards;
  opacity: 0;
}

.service-card {
  animation: serviceCard 0.6s ease-out forwards;
  opacity: 0;
}

.client-logos-scroll {
  animation: clientLogosScroll 20s linear infinite;
}

/* Floating orbs for hero section */
.floating-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(20px);
  pointer-events: none;
}

.floating-orb-1 {
  top: 25%;
  left: 25%;
  width: 160px;
  height: 160px;
  background: radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent);
  animation: floatingOrb1 12s ease-in-out infinite;
}

.floating-orb-2 {
  top: 33%;
  right: 25%;
  width: 128px;
  height: 128px;
  background: linear-gradient(45deg, rgba(168, 85, 247, 0.2), rgba(59, 130, 246, 0.2));
  border-radius: 20px;
  filter: blur(15px);
  animation: floatingOrb2 10s ease-in-out infinite;
  animation-delay: 2s;
}

.floating-orb-3 {
  bottom: 25%;
  left: 33%;
  width: 96px;
  height: 96px;
  background: conic-gradient(from 0deg, rgba(34, 197, 94, 0.2), rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2));
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  filter: blur(12px);
  animation: floatingOrb3 8s ease-in-out infinite;
  animation-delay: 4s;
}

.floating-orb-4 {
  top: 50%;
  right: 33%;
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(59, 130, 246, 0.2));
  animation: floatingOrb1 15s ease-in-out infinite;
  animation-delay: 1s;
}

.floating-orb-5 {
  bottom: 33%;
  right: 25%;
  width: 112px;
  height: 112px;
  background: radial-gradient(ellipse at center, rgba(236, 72, 153, 0.2), transparent);
  filter: blur(18px);
  animation: floatingOrb2 9s ease-in-out infinite;
  animation-delay: 6s;
}
