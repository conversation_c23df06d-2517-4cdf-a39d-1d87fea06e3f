import Image from 'next/image';
import Link from 'next/link';
import { ArrowRightIcon, NewspaperIcon } from '@heroicons/react/24/outline';

interface BlogPost {
  id: string;
  title: string;
  excerpt?: string;
  content?: string;
  slug: string;
  featuredImageUrl?: string;
  publishedAt?: string;
  createdAt: string;
  readTime?: string;
  categories?: string;
}

interface BlogSectionProps {
  blogPosts: BlogPost[];
}

export function BlogSection({ blogPosts }: BlogSectionProps) {
  return (
    <section className="py-24 bg-gradient-to-br from-white via-gray-50/50 to-blue-50/30">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Latest <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Insights</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Stay updated with the latest trends, tips, and insights from our team
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {blogPosts.length > 0 ? (
            blogPosts.map((post, index) => (
              <article
                key={post.id}
                className="service-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <Link href={`/blog/${post.slug}`} className="group block h-full">
                  <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full flex flex-col">
                    {post.featuredImageUrl ? (
                      <div className="h-48 relative overflow-hidden">
                        <Image
                          src={post.featuredImageUrl}
                          alt={post.title}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          loading="lazy"
                        />
                      </div>
                    ) : (
                      <div className="h-48 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] flex items-center justify-center">
                        <NewspaperIcon className="h-16 w-16 text-blue-700" />
                      </div>
                    )}
                    <div className="p-6 flex flex-col flex-grow">
                      <div className="flex items-center text-sm text-gray-500 mb-3">
                        <span>
                          {new Date(post.publishedAt || post.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                        </span>
                        <span className="mx-2">•</span>
                        <span>{post.readTime || '5 min read'}</span>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 mb-4 flex-grow">
                        {post.excerpt || post.content?.substring(0, 150) + '...'}
                      </p>
                      {post.categories && (
                        <div className="flex flex-wrap gap-2 mb-4">
                          {post.categories.split(',').slice(0, 2).map((category, categoryIndex) => (
                            <span
                              key={categoryIndex}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {category.trim()}
                            </span>
                          ))}
                        </div>
                      )}
                      <div className="flex items-center text-blue-600 font-semibold group-hover:translate-x-2 transition-transform duration-300 mt-auto">
                        Read More
                        <ArrowRightIcon className="ml-2 h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </Link>
              </article>
            ))
          ) : (
            // Fallback content when no blog posts are available
            Array.from({ length: 3 }, (_, index) => (
              <article
                key={index}
                className="service-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden">
                  <div className="h-48 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] flex items-center justify-center">
                    <NewspaperIcon className="h-16 w-16 text-blue-700" />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center text-sm text-gray-500 mb-3">
                      <span>Coming Soon</span>
                      <span className="mx-2">•</span>
                      <span>5 min read</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      Blog Post Coming Soon
                    </h3>
                    <p className="text-gray-600 mb-4">
                      We're working on exciting content for you. Stay tuned for our latest insights and updates.
                    </p>
                  </div>
                </div>
              </article>
            ))
          )}
        </div>

        <div className="section-header text-center">
          <Link
            href="/blog"
            className="inline-flex items-center px-8 py-4 bg-white border border-gray-200 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
          >
            View All Articles
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
