#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Homepage Performance...\n');

// Check if build exists
const buildDir = path.join(__dirname, '../.next');
if (!fs.existsSync(buildDir)) {
  console.log('📦 Building application first...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

console.log('✅ Build completed successfully!\n');

// Performance checklist
const checklist = [
  {
    name: 'Static Site Generation (SSG)',
    description: 'Homepage should be statically generated',
    check: () => {
      const buildManifest = path.join(__dirname, '../.next/server/pages-manifest.json');
      if (fs.existsSync(buildManifest)) {
        const manifest = JSON.parse(fs.readFileSync(buildManifest, 'utf8'));
        return manifest['/'] && manifest['/'].endsWith('.html');
      }
      return false;
    }
  },
  {
    name: 'Image Optimization',
    description: 'Next.js Image component configured properly',
    check: () => {
      const nextConfig = path.join(__dirname, '../next.config.ts');
      if (fs.existsSync(nextConfig)) {
        const config = fs.readFileSync(nextConfig, 'utf8');
        return config.includes('formats') && config.includes('webp');
      }
      return false;
    }
  },
  {
    name: 'CSS Animations',
    description: 'CSS animations instead of JavaScript animations',
    check: () => {
      const globalCSS = path.join(__dirname, '../src/app/globals.css');
      if (fs.existsSync(globalCSS)) {
        const css = fs.readFileSync(globalCSS, 'utf8');
        return css.includes('@keyframes') && css.includes('heroContentAnimate');
      }
      return false;
    }
  },
  {
    name: 'Component Splitting',
    description: 'Homepage split into smaller components',
    check: () => {
      const homeDir = path.join(__dirname, '../src/components/home');
      if (fs.existsSync(homeDir)) {
        const files = fs.readdirSync(homeDir);
        return files.length >= 10; // Should have at least 10 component files
      }
      return false;
    }
  },
  {
    name: 'Bundle Optimization',
    description: 'Package imports optimized',
    check: () => {
      const nextConfig = path.join(__dirname, '../next.config.ts');
      if (fs.existsSync(nextConfig)) {
        const config = fs.readFileSync(nextConfig, 'utf8');
        return config.includes('optimizePackageImports');
      }
      return false;
    }
  }
];

console.log('📋 Performance Optimization Checklist:\n');

let passedChecks = 0;
checklist.forEach((item, index) => {
  const passed = item.check();
  const status = passed ? '✅' : '❌';
  console.log(`${index + 1}. ${status} ${item.name}`);
  console.log(`   ${item.description}`);
  if (passed) passedChecks++;
  console.log('');
});

const score = Math.round((passedChecks / checklist.length) * 100);
console.log(`🎯 Optimization Score: ${score}% (${passedChecks}/${checklist.length} checks passed)\n`);

// Performance recommendations
console.log('🚀 Next Steps for Performance Testing:\n');
console.log('1. 📊 Test with Lighthouse:');
console.log('   - Open Chrome DevTools');
console.log('   - Go to Lighthouse tab');
console.log('   - Run Performance audit');
console.log('   - Target: 90+ Performance score\n');

console.log('2. 🌐 Test with PageSpeed Insights:');
console.log('   - Visit: https://pagespeed.web.dev/');
console.log('   - Enter your deployed URL');
console.log('   - Check both Mobile and Desktop scores\n');

console.log('3. 📈 Monitor Core Web Vitals:');
console.log('   - LCP (Largest Contentful Paint): < 2.5s');
console.log('   - FID (First Input Delay): < 100ms');
console.log('   - CLS (Cumulative Layout Shift): < 0.1\n');

console.log('4. 🔧 Additional Optimizations:');
console.log('   - Add optimized images to public/images/');
console.log('   - Test on slow 3G network');
console.log('   - Verify mobile performance');
console.log('   - Check accessibility scores\n');

// Bundle analysis recommendation
console.log('5. 📦 Analyze Bundle Size:');
console.log('   Run: ANALYZE=true npm run build');
console.log('   This will open bundle analyzer in your browser\n');

if (score >= 80) {
  console.log('🎉 Great job! Your homepage is well-optimized for performance.');
} else if (score >= 60) {
  console.log('⚠️  Good progress! A few more optimizations needed.');
} else {
  console.log('🔧 More work needed. Review the failed checks above.');
}

console.log('\n📚 For more optimization tips, see: HOMEPAGE-OPTIMIZATION-GUIDE.md');
